{"$schema": "./opencode-schema.json", "lsp": {"gopls": {"command": "gopls"}}, "providers": {"openrouter": {"apiKey": "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", "disabled": false}}, "agents": {"coder": {"model": "openrouter.claude-3.7-sonnet", "maxTokens": 8192, "reasoningEffort": "medium"}, "summarizer": {"model": "openrouter.claude-3.7-sonnet", "maxTokens": 3000}, "task": {"model": "openrouter.claude-3.7-sonnet", "maxTokens": 2000}, "title": {"model": "openrouter.claude-3.7-sonnet", "maxTokens": 80}}}
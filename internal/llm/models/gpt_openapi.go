package models

const (
	ProviderGPTOpenapi ModelProvider = "gpt_openapi"

	GPTOpenapiClaude37Sonnet ModelID = "gcp_claude37_sonnet"
)

var GPTOpenapiModels = map[ModelID]Model{
	GPTOpenapiClaude37Sonnet: {
		ID:                 GPTOpenapiClaude37Sonnet,
		Name:               "GPT Openapi – Claude 3.7 Sonnet",
		Provider:           ProviderGPTOpenapi,
		APIModel:           "gcp_claude37_sonnet",
		CostPer1MIn:        AnthropicModels[Claude37Sonnet].CostPer1MIn,
		CostPer1MInCached:  AnthropicModels[Claude37Sonnet].CostPer1MInCached,
		CostPer1MOut:       AnthropicModels[Claude37Sonnet].CostPer1MOut,
		CostPer1MOutCached: AnthropicModels[Claude37Sonnet].CostPer1MOutCached,
		ContextWindow:      AnthropicModels[Claude37Sonnet].ContextWindow,
		DefaultMaxTokens:   AnthropicModels[<PERSON><PERSON><PERSON>onnet].DefaultMaxTokens,
		CanReason:          AnthropicModels[Claude37Sonnet].CanReason,
	},
}
